# Learning Coach Community

React Native/Expo app for the Learning Coach Community

## Directories

- `assets` (@assets)
    - `images`
    - `fonts`
- `src` (@/) - The main app directory
    - `api/` (@/api): Contains API calls and network-related code.
    - `components/` (@/components): Reusable UI components.
    - `config/` (@/config): Configuration files, constants, and environment variables.
    - `hooks/` (@/hooks): Custom hooks.
    - `navigation/` (@/navigation): Navigation setup and configurations.
    - `repositories/` (@/repositories): Data access layers, interfaces for APIs, databases, etc.
    - `screens/` (@/screens): Screen components.
    - `services/` (@/services): Business logic, interacting with repositories.
    - `styles/` (@/styles): Styling
    - `theme/` (@/theme): Colors, Tokens and Theme colors for native commponents.
    - `usecases/` (@/usecases): Specific application use cases or business rules.
    - `utils/` (@/utils): Utility functions.

## Architecture

This project follows **Clean Architecture** principles to ensure maintainability, testability, and separation of concerns. Understanding these patterns is crucial for consistent development across the team.

### Core Principles

1. **Dependency Inversion**: Higher-level modules should not depend on lower-level modules. Both should depend on abstractions.
2. **Single Responsibility**: Each class/module should have one reason to change.
3. **Separation of Concerns**: Different aspects of the application should be handled by different layers.
4. **Testability**: Each layer should be independently testable through dependency injection and mocking.

### Layer Definitions

#### **Services Layer** (`/src/services/`)

- **Purpose**: Business logic and application workflows
- **Responsibilities**:
    - Domain-specific business rules and validation
    - Complex business operations and orchestration
    - Application-specific logic and workflows
    - Business rule enforcement and data transformation
    - Coordinating between multiple repositories
- **Dependencies**: Repositories layer
- **Examples**: `authService.ts`, `userService.ts`, `apiConfigService.ts`

#### **Repositories Layer** (`/src/repositories/`)

- **Purpose**: Data access and storage abstraction
- **Responsibilities**:
    - Data access interfaces and CRUD operations
    - Storage mechanism abstraction (APIs, databases, local storage)
    - Platform-specific implementations (iOS/Android/Web differences)
    - Third-party library integrations (Expo SecureStore, AsyncStorage, etc.)
    - Low-level data operations and persistence
- **Dependencies**: External libraries, platform APIs
- **Examples**: `secureStorageRepository.ts`, `userRepository.ts`, `apiRepository.ts`

#### **API Layer** (`/src/api/`)

- **Purpose**: External communication interfaces
- **Responsibilities**:
    - HTTP request/response handling
    - API endpoint definitions
    - Request/response transformation
    - Network error handling
- **Dependencies**: Repositories layer (for data access)
- **Examples**: `authApi.ts`, `userApi.ts`, `contentApi.ts`

#### **Components Layer** (`/src/components/`)

- **Purpose**: Reusable UI elements
- **Responsibilities**:
    - Pure UI logic
    - Component-specific state management
    - Props validation and transformation
    - Accessibility implementations
- **Dependencies**: Hooks, Utils
- **Examples**: `Button.tsx`, `Modal.tsx`, `UserCard.tsx`

#### **Screens Layer** (`/src/screens/`)

- **Purpose**: Application screens and navigation targets
- **Responsibilities**:
    - Screen-level state management
    - Coordinate UI components
    - Handle navigation logic
    - Connect to use cases and services
- **Dependencies**: Components, Hooks, Use Cases, Services
- **Examples**: `LoginScreen.tsx`, `ProfileScreen.tsx`, `HomeScreen.tsx`

#### **Hooks Layer** (`/src/hooks/`)

- **Purpose**: Reusable stateful logic
- **Responsibilities**:
    - Custom React hooks for shared logic
    - State management patterns
    - Side effect management
    - Component lifecycle abstractions
- **Dependencies**: Use Cases, Services, Repositories
- **Examples**: `useAuth.ts`, `useSecureStorage.ts`, `useApi.ts`

### Dependency Flow

```
Screens → Hooks → Services → Repositories → External APIs/Storage
   ↓         ↓         ↓           ↓            ↓
Components   Utils   Config    Domain Models  Platform APIs
```

### Best Practices

#### **Naming Conventions**

- **Services**: `[domain]Service.ts` (e.g., `authService.ts`, `userService.ts`)
- **Repositories**: `[domain]Repository.ts` (e.g., `secureStorageRepository.ts`, `apiRepository.ts`)
- **Use Cases**: `[action][Domain].ts` (e.g., `authenticateUser.ts`, `fetchUserProfile.ts`)
- **APIs**: `[domain]Api.ts` (e.g., `authApi.ts`)
- **Hooks**: `use[Domain].ts` (e.g., `useAuth.ts`)

#### **Error Handling**

- Repositories should throw technical/infrastructure errors
- Services should handle business logic errors and validation
- Components should handle UI-specific error states

#### **Testing Strategy**

- **Unit Tests**: Test each layer in isolation using mocks
- **Integration Tests**: Test layer interactions
- **E2E Tests**: Test complete user workflows

#### **Import Rules**

- Never import from a higher layer (e.g., Repositories should not import from Services)
- Use dependency injection for testability
- Prefer interfaces over concrete implementations for dependencies

## State Management with Redux

This project uses **Redux Toolkit** for global state management, providing a predictable state container for the application.

### Redux Store Configuration

The Redux store is configured in `src/store.ts`:

<augment_code_snippet path="src/store.ts" mode="EXCERPT">

```typescript
import { configureStore } from '@reduxjs/toolkit';
import helloReducer from './features/hello/helloSlice';

export const store = configureStore({
	reducer: {
		hello: helloReducer,
		// other reducers go here...
	},
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

</augment_code_snippet>

### Feature-Based Organization // TODO: confirm this ai-generated suggestion

Redux logic is organized using the **feature folder pattern** under `src/features/`:

- Each feature has its own directory (e.g., `src/features/hello/`)
- Slices are defined using Redux Toolkit's `createSlice` API
- Actions and reducers are co-located within each slice

### Example Slice Structure

<augment_code_snippet path="src/features/hello/helloSlice.tsx" mode="EXCERPT">

```typescript
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface HelloState {
	message: string;
}

const helloSlice = createSlice({
	name: 'hello',
	initialState,
	reducers: {
		setMessage: (state, action: PayloadAction<string>) => {
			state.message = action.payload;
		},
		resetMessage: (state) => {
			state.message = 'Hello World';
		},
	},
});
```

</augment_code_snippet>

### Usage in Components

To use Redux in components:

1. **Reading State**: Use `useSelector` hook with typed `RootState`
2. **Dispatching Actions**: Use `useDispatch` hook with typed `AppDispatch`

```typescript
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '@/store';
import { setMessage, resetMessage } from '@/features/hello/helloSlice';

const MyComponent = () => {
  const message = useSelector((state: RootState) => state.hello.message);
  const dispatch = useDispatch<AppDispatch>();

  const handleUpdateMessage = () => {
    dispatch(setMessage('New message'));
  };

  const handleReset = () => {
    dispatch(resetMessage());
  };

  return (
    // Your component JSX
  );
};
```

### Best Practices // TODO: confirm these ai-generated suggestions

- **Feature-based organization**: Keep related state, actions, and reducers together
- **Typed selectors**: Always use `RootState` type for type-safe state access
- **Typed dispatch**: Use `AppDispatch` type for proper action typing
- **Immutable updates**: Redux Toolkit uses Immer internally, allowing "mutative" logic
- **Async operations**: Use `createAsyncThunk` for handling async operations
- **Theme**: Use `useTheme()` from `@/theme` for handling Colors with Dark & Light theme.
- **Tokens** Use `Tokens` from `@theme` for handling Measures `Tokens.Spacing.m`
- **Localisation** Use t('feature.key_name") from

## Get started

1. Install dependencies

    ```bash
    npm install
    ```

2. Start the app

    ```bash
    npx expo start
    ```

In the output, you'll find options to open the app in a

- [development build](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

You can start developing by editing the files inside the **app** directory. This project uses [file-based routing](https://docs.expo.dev/router/introduction).

## Development Configuration

### Optional dev.config.json

For development convenience, you can create a `dev.config.json` file in the project root to automatically load API configuration. This file is optional and handled gracefully by the Metro bundler.

#### Setup

1. Copy the template file:

    ```bash
    cp dev.config.json.template dev.config.json
    ```

2. Edit `dev.config.json` with your development API credentials:

    ```json
    {
    	"DEV_UNA_API_KEY": "your-development-api-key-here",
    	"DEV_UNA_API_BASE_URL": "https://your-dev-api-url.com"
    }
    ```

3. The app will automatically detect and load these values in development mode.

#### How it works

- **Metro Resolver**: The Metro bundler is configured to handle the optional `dev.config.json` file
- **Fallback**: If the file doesn't exist, a fallback configuration is used to prevent build errors
- **Security**: The `dev.config.json` file is excluded from git via `.gitignore`
- **Development Only**: Configuration loading only works in development mode (`__DEV__ === true`)

#### Manual Configuration

If you prefer not to use `dev.config.json`, you can manually enter your API configuration in the app's API Keys screen.

## Learn more

To learn more about developing your project with Expo, look at the following resources:

- [Expo documentation](https://docs.expo.dev/): Learn fundamentals, or go into advanced topics with our [guides](https://docs.expo.dev/guides).
- [Learn Expo tutorial](https://docs.expo.dev/tutorial/introduction/): Follow a step-by-step tutorial where you'll create a project that runs on Android, iOS, and the web.

## Join the community

Join our community of developers creating universal apps.

- [Expo on GitHub](https://github.com/expo/expo): View our open source platform and contribute.
- [Discord community](https://chat.expo.dev): Chat with Expo users and ask questions.
