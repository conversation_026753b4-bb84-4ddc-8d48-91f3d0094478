// https://docs.expo.dev/guides/using-eslint/
const { defineConfig } = require('eslint/config');
const expoConfig = require('eslint-config-expo/flat');

module.exports = defineConfig([
	// Spread Expo's flat config (it exports an array)
	...expoConfig,
	// Ignore build artifacts
	{
		ignores: ['dist/*'],
	},
	// Global rules adjustments
	{
		rules: {
			// TS + RN projects commonly rely on TS path resolution; avoid false positives
			'import/no-unresolved': 'off',
		},
	},
	// Jest/Test environment overrides
	{
		files: ['**/*.test.{js,jsx,ts,tsx}', 'jest.setup.js', 'jest/setup.ts'],
		languageOptions: {
			globals: {
				jest: 'readonly',
				expect: 'readonly',
				describe: 'readonly',
				it: 'readonly',
				beforeEach: 'readonly',
				afterEach: 'readonly',
				beforeAll: 'readonly',
				afterAll: 'readonly',
			},
		},
	},
]);
