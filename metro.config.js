const { withNativeWind } = require('nativewind/metro');
const { getDefaultConfig } = require('expo/metro-config');
const fs = require('fs');
const path = require('path');

const config = getDefaultConfig(__dirname);
config.resolver.blockList = [/(.*.test.tsx?)$/];

// Handle optional dev.config.json file
const devConfigPath = path.resolve(__dirname, 'dev.config.json');
const devConfigExists = fs.existsSync(devConfigPath);

console.log('🔧 Metro: dev.config.json exists:', devConfigExists);
console.log('🔧 Metro: dev.config.json path:', devConfigPath);

// Always set up the resolver alias to handle both cases
config.resolver.alias = {
	...config.resolver.alias,
	'@dev-config': devConfigExists
		? devConfigPath
		: path.resolve(__dirname, 'src/config/dev.config.fallback.js'),
};

module.exports = withNativeWind(config, { input: './global.css' });
