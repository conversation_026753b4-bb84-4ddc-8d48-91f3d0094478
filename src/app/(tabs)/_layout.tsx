import { Tabs } from 'expo-router';
import React from 'react';
import 'global.css';
import { Platform } from 'react-native';

import { HapticTab } from '@/components/HapticTab';
import { IconSymbol } from '@/components/ui/IconSymbol';
import TabBarBackground from '@/components/ui/TabBarBackground';
import LogoutButton from '@/components/LogoutButton';
import { useTheme, Tokens } from '@/theme';
export default function TabLayout() {
	const theme = useTheme();
	return (
		<Tabs
			screenOptions={{
				tabBarActiveTintColor: theme.colors.tint,
				headerShown: true,
				headerStyle: {
					backgroundColor: theme.colors.background,
				},
				headerTintColor: theme.colors.text,
				headerTitleStyle: {
					...theme.fonts.bold,
				},
				headerRight: () => <LogoutButton variant='header' />,
				tabBarButton: HapticTab,
				tabBarBackground: TabBarBackground,
				tabBarStyle: Platform.select({
					ios: {
						// Use a transparent background on iOS to show the blur effect
						position: 'absolute',
					},
					default: {},
				}),
			}}
		>
			<Tabs.Screen
				name='index'
				options={{
					title: 'Home',
					tabBarIcon: ({ color }) => (
						<IconSymbol size={Tokens.Sizes.icon} name='house.fill' color={color} />
					),
				}}
			/>
			<Tabs.Screen
				name='explore'
				options={{
					title: 'Explore',
					tabBarIcon: ({ color }) => (
						<IconSymbol size={Tokens.Sizes.icon} name='paperplane.fill' color={color} />
					),
				}}
			/>
			<Tabs.Screen
				name='dev-tools'
				options={{
					title: 'Debug',
					tabBarIcon: ({ color }) => (
						<IconSymbol size={Tokens.Sizes.icon} name='hammer.fill' color={color} />
					),
				}}
			/>
		</Tabs>
	);
}
