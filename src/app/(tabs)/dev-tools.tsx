import { Image } from 'expo-image';
import { useEffect, useRef, useState } from 'react';
import { ActivityIndicator, Modal, StyleSheet, View } from 'react-native';
import { useSelector } from 'react-redux';

import { HelloWave } from '@/components/HelloWave';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import CustomButton from '@/components/ui/CustomButton';
import CustomTextField from '@/components/ui/CustomTextField';
import { useRouter } from 'expo-router';
import Config from '@/config';
import { useTranslation } from 'react-i18next';
import { selectUser, selectIsAuthenticated } from '@/features/user/userSlice';
import { Tokens } from '@/theme';
import { createAuthService } from '@/services/authService';
import AuthWebView from '@/components/AuthWebView';

export default function HomeScreen() {
  const [waveTrigger, setWaveTrigger] = useState(0);
  const [showAuthWebView, setShowAuthWebView] = useState(false);
  const [loginUrl, setLoginUrl] = useState<string | null>(null);
  const [ssoStatus, setSsoStatus] = useState<'idle' | 'success' | 'failed'>('idle');
  const [lastSsoAt, setLastSsoAt] = useState<number | null>(null);
  const authServiceRef = useRef<Awaited<ReturnType<typeof createAuthService>> | null>(null);
  const router = useRouter();
  const { t } = useTranslation();
  const user = useSelector(selectUser);
  const isAuthenticated = useSelector(selectIsAuthenticated);

  useEffect(() => {
    // Preload AuthService so the modal opens quickly
    (async () => {
      try {
        authServiceRef.current = await createAuthService();
        console.log('AuthService initialized with URL:', authServiceRef.current.getLoginUrl());
        setLoginUrl(authServiceRef.current.getLoginUrl());
      } catch (e) {
        if (__DEV__) console.warn('[DevTools] Failed to init AuthService', e);
      }
    })();
  }, []);

  const handleOpenSso = () => {
    setSsoStatus('idle');
    setLastSsoAt(null);
    setShowAuthWebView(true);
  };

  const handleCloseWebView = () => {
    setShowAuthWebView(false);
    setLastSsoAt(Date.now());
    setSsoStatus(isAuthenticated ? 'success' : 'failed');
  };

  return (
    <>
      <ParallaxScrollView
        headerBackgroundColor={{ light: '#A1CEDC', dark: '#1D3D47' }}
        headerImage={
          <Image source={require('@assets/images/logo.svg')} style={styles.reactLogo} />
        }
      >
        {/* TODO: Drop it later
          User greeting from UserContext 
        */}
        {isAuthenticated && user && (
          <ThemedView style={styles.greetingContainer}>
            <ThemedText type='title'>
              Hello {user.username || user.display_name} - Pulled from User Context
            </ThemedText>
            <ThemedText type='default' style={styles.userInfo}>
              Account ID: {user.account_id} | Role: {user.role} | Status: {user.status}
            </ThemedText>
          </ThemedView>
        )}

        {!isAuthenticated && (
          <ThemedView style={styles.greetingContainer}>
            <CustomButton
              variant='primary'
              onPress={() => {
                router.push('login');
              }}
            >
              {'Login'}
            </CustomButton>
          </ThemedView>
        )}

        <ThemedView style={styles.titleContainer}>
          <CustomTextField variant='primary'>{t('dev_home.welcome')}</CustomTextField>
          <HelloWave waveTrigger={waveTrigger} />
          <CustomButton variant='primary' onPress={() => setWaveTrigger((prev) => prev + 1)}>
            {t('dev_home.waveAgain')}
          </CustomButton>
        </ThemedView>

        <ThemedView style={styles.stepContainer}>
          <ThemedText type='title'>{t('dev_home.config')}</ThemedText>
          <ThemedText>{Config.ENV_NAME}</ThemedText>
          <ThemedText>{Config.API_URL}</ThemedText>
          <ThemedText>{Config.CONFIG_VERSION}</ThemedText>
        </ThemedView>

        <ThemedView style={styles.stepContainer}>
          <ThemedText type='title'>{t('dev_home.navigation')}</ThemedText>
          <CustomButton
            variant='primary'
            onPress={() => {
              router.push('hello');
            }}
          >
            {t('dev_home.navigateButton')}
          </CustomButton>
        </ThemedView>

        <ThemedView style={styles.stepContainer}>
          <ThemedText type='title'>{t('dev_home.apiKeysHeader')}</ThemedText>
          <CustomButton
            variant='primary'
            onPress={() => {
              router.push('api-keys');
            }}
          >
            {t('dev_home.apiKeysButton')}
          </CustomButton>
        </ThemedView>

        {/* Dev-only: Test SSO via WebView */}
        <ThemedView style={styles.stepContainer}>
          <ThemedText type='title'>SSO</ThemedText>
          <CustomButton
            variant='primary'
            onPress={handleOpenSso}
          >
            Test SSO (WebView)
          </CustomButton>
          <ThemedText type='default'>
            {ssoStatus === 'idle'
              ? 'No SSO attempt yet.'
              : ssoStatus === 'success'
              ? `SSO verified${user ? ` for ${user.username || user.display_name}` : ''}${
                  lastSsoAt ? ` at ${new Date(lastSsoAt).toLocaleTimeString()}` : ''
                }`
              : `SSO failed or was cancelled${
                  lastSsoAt ? ` at ${new Date(lastSsoAt).toLocaleTimeString()}` : ''
                }.`}
          </ThemedText>
        </ThemedView>
      </ParallaxScrollView>

      {/* Full-screen modal with WebView for auth */}
      <Modal
        visible={showAuthWebView}
        animationType='slide'
        presentationStyle='fullScreen'
        onRequestClose={handleCloseWebView}
      >
        <View style={{ flex: 1 }}>
          {!loginUrl ? (
            <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
              <ActivityIndicator />
            </View>
          ) : (
            <AuthWebView
              loginUrl={loginUrl}
              authServiceRef={authServiceRef}
              onClose={handleCloseWebView}
            />
          )}
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  greetingContainer: {
    marginBottom: Tokens.Spacing.m,
    padding: Tokens.Spacing.m,
    borderRadius: Tokens.Radius.s,
    backgroundColor: 'rgba(161, 206, 220, 0.1)',
  },
  userInfo: {
    gap: Tokens.Spacing.s,
    opacity: 0.8,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Tokens.Spacing.s,
  },
  stepContainer: {
    gap: Tokens.Spacing.s,
    marginBottom: Tokens.Spacing.s,
  },
  reactLogo: {
    height: 48,
    width: 96,
    top: 30,
    left: 20,
    position: 'absolute',
  },
});
