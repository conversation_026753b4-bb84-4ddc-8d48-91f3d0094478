import { Image } from 'expo-image';
import { Platform, StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';

import { Collapsible } from '@/components/Collapsible';
import { ExternalLink } from '@/components/ExternalLink';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';

export default function TabTwoScreen() {
	const { t } = useTranslation();

	return (
		<ParallaxScrollView
			headerBackgroundColor={{ light: '#D0D0D0', dark: '#353636' }}
			headerImage={
				<IconSymbol
					size={310}
					color='#808080'
					name='chevron.left.forwardslash.chevron.right'
					style={styles.headerImage}
				/>
			}
		>
			<ThemedView style={styles.titleContainer}>
				<ThemedText type='title'>{t('dev_explore.title')}</ThemedText>
			</ThemedView>

			<ThemedText>{t('dev_explore.intro')}</ThemedText>

			<Collapsible title={t('dev_explore.fileRouting.title')}>
				<ThemedText>{t('dev_explore.fileRouting.desc1')}</ThemedText>
				<ThemedText>{t('dev_explore.fileRouting.desc2')}</ThemedText>
				<ExternalLink href='https://docs.expo.dev/router/introduction'>
					<ThemedText type='link'>{t('dev_explore.fileRouting.learnMore')}</ThemedText>
				</ExternalLink>
			</Collapsible>

			<Collapsible title={t('dev_explore.platformSupport.title')}>
				<ThemedText>{t('dev_explore.platformSupport.desc')}</ThemedText>
			</Collapsible>

			<Collapsible title={t('dev_explore.images.title')}>
				<ThemedText>{t('dev_explore.images.desc')}</ThemedText>
				<Image
					source={require('@assets/images/react-logo.png')}
					style={{ alignSelf: 'center' }}
				/>
				<ExternalLink href='https://reactnative.dev/docs/images'>
					<ThemedText type='link'>{t('dev_explore.images.learnMore')}</ThemedText>
				</ExternalLink>
			</Collapsible>

			<Collapsible title={t('dev_explore.fonts.title')}>
				<ThemedText>{t('dev_explore.fonts.desc1')}</ThemedText>
				<ExternalLink href='https://docs.expo.dev/versions/latest/sdk/font'>
					<ThemedText type='link'>{t('dev_explore.fonts.learnMore')}</ThemedText>
				</ExternalLink>
			</Collapsible>

			<Collapsible title={t('dev_explore.themes.title')}>
				<ThemedText>{t('dev_explore.themes.desc')}</ThemedText>
				<ExternalLink href='https://docs.expo.dev/develop/user-interface/color-themes/'>
					<ThemedText type='link'>{t('dev_explore.themes.learnMore')}</ThemedText>
				</ExternalLink>
			</Collapsible>

			<Collapsible title={t('dev_explore.animations.title')}>
				<ThemedText>{t('dev_explore.animations.desc1')}</ThemedText>
				{Platform.select({
					ios: <ThemedText>{t('dev_explore.animations.desc2')}</ThemedText>,
				})}
			</Collapsible>
		</ParallaxScrollView>
	);
}

const styles = StyleSheet.create({
	headerImage: {
		color: '#808080',
		bottom: -90,
		left: -35,
		position: 'absolute',
	},
	titleContainer: {
		flexDirection: 'row',
		gap: 8,
	},
});
