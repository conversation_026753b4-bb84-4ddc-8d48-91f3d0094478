import { Image } from 'expo-image';
import { useState } from 'react';
import { StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';

import { HelloWave } from '@/components/HelloWave';
import ParallaxScrollView from '@/components/ParallaxScrollView';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import CustomButton from '@/components/ui/CustomButton';
import CustomTextField from '@/components/ui/CustomTextField';
import { useRouter } from 'expo-router';
import Config from '@/config';
import { useTranslation } from 'react-i18next';
import { selectUser, selectIsAuthenticated } from '@/features/user/userSlice';
import { Tokens } from '@/theme';
export default function HomeScreen() {
	const [waveTrigger, setWaveTrigger] = useState(0);
	const router = useRouter();
	const { t } = useTranslation();
	const user = useSelector(selectUser);
	const isAuthenticated = useSelector(selectIsAuthenticated);

	return (
		<ParallaxScrollView
			headerBackgroundColor={{ light: '#A1CEDC', dark: '#1D3D47' }}
			headerImage={
				<Image source={require('@assets/images/logo.svg')} style={styles.reactLogo} />
			}
		>
			{/* TODO: Drop it later
				User greeting from UserContext 
			*/}
			{isAuthenticated && user && (
				<ThemedView style={styles.greetingContainer}>
					<ThemedText type='title'>
						Hello {user.username || user.display_name} - Pulled from User Context
					</ThemedText>
					<ThemedText type='default' style={styles.userInfo}>
						Account ID: {user.account_id} | Role: {user.role} | Status: {user.status}
					</ThemedText>
				</ThemedView>
			)}
		</ParallaxScrollView>
	);
}

const styles = StyleSheet.create({
	greetingContainer: {
		marginBottom: Tokens.Spacing.m,
		padding: Tokens.Spacing.m,
		borderRadius: Tokens.Radius.s,
		backgroundColor: 'rgba(161, 206, 220, 0.1)',
	},
	userInfo: {
		marginBottom: Tokens.Spacing.s,
		opacity: 0.8,
	},
	titleContainer: {
		flexDirection: 'row',
		alignItems: 'center',
		gap: Tokens.Spacing.s,
	},
	stepContainer: {
		gap: Tokens.Spacing.s,
		marginBottom: Tokens.Spacing.s,
	},
	reactLogo: {
		height: 48,
		width: 96,
		top: Tokens.Spacing.l,
		left: Tokens.Spacing.m,
		position: 'absolute',
	},
});
