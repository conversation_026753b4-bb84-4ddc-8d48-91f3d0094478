import { ThemeProvider } from '@react-navigation/native';
import { DefaultTheme } from '@/theme';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import 'react-native-reanimated';

import { Provider } from 'react-redux';
import { store } from '@/store';

import { I18nextProvider } from 'react-i18next';
import i18n, { useI18n } from '@/translations';
import { useColorScheme } from 'react-native';

export default function RootLayout() {
	const colorScheme = useColorScheme();
	const [fontsLoaded] = useFonts({
		SpaceMono: require('@assets/fonts/SpaceMono-Regular.ttf'),
	});
	const [translationsLoaded] = useI18n();

	if (!fontsLoaded || !translationsLoaded) {
		// Async font loading only occurs in development.
		return null;
	}

	return (
		<ThemeProvider value={colorScheme === 'dark' ? DefaultTheme : DefaultTheme}>
			<Provider store={store}>
				<I18nextProvider i18n={i18n}>
					<Stack>
						<Stack.Screen
							name='login'
							options={{ headerShown: false, title: 'Login' }}
						/>
						<Stack.Screen
							name='(tabs)'
							options={{ headerShown: false, title: 'Home' }}
						/>
						<Stack.Screen name='+not-found' />
						<Stack.Screen name='hello' options={{ title: 'Hello Screen' }} />
					</Stack>
				</I18nextProvider>
			</Provider>
			<StatusBar style='auto' />
		</ThemeProvider>
	);
}
