import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { store } from '@/store';
import HelloScreen from './hello';
import { Alert } from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { DefaultTheme } from '@/theme';

jest.spyOn(Alert, 'alert');

let mockSetOptions: jest.Mock;

jest.mock('expo-router', () => {
	mockSetOptions = jest.fn(); // ✅
	return {
		useNavigation: () => ({
			setOptions: mockSetOptions,
		}),
	};
});

describe('<HelloScreen />', () => {
	it('renders the text "Hello World"', () => {
		const { getByText } = render(
			<Provider store={store}>
				<NavigationContainer theme={DefaultTheme}>
					<HelloScreen />
				</NavigationContainer>
			</Provider>,
		);
		expect(getByText('dev_hello.defaultMessage')).toBeTruthy();
	});

	it('calls Alert.alert when the header button is pressed', () => {
		render(
			<Provider store={store}>
				<NavigationContainer theme={DefaultTheme}>
					<HelloScreen />
				</NavigationContainer>
			</Provider>,
		);

		const headerConfig = mockSetOptions.mock.calls[0][0];
		const HeaderRightButton = headerConfig.headerRight;

		const { getByText } = render(<HeaderRightButton />);
		fireEvent.press(getByText('dev_hello.buttonAlert'));

		expect(Alert.alert).toHaveBeenCalledWith('dev_hello.buttonAlert');
	});
});
