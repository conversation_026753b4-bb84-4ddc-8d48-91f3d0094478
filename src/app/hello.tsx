import { StyleSheet, <PERSON><PERSON>, But<PERSON> } from 'react-native';
import { useNavigation } from 'expo-router';
import { useLayoutEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import type { RootState, AppDispatch } from '@/store';
import { setMessage, resetMessage } from '@/features/hello/helloSlice';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import CustomButton from '@/components/ui/CustomButton';
import { useTranslation } from 'react-i18next';

export default function HelloScreen() {
	const navigation = useNavigation();
	const dispatch = useDispatch<AppDispatch>();
	const message = useSelector((state: RootState) => state.hello.message);
	const { t, i18n } = useTranslation();

	useLayoutEffect(() => {
		navigation.setOptions({
			title: t('dev_hello.title'),
			headerRight: () => (
				<Button
					onPress={() => Alert.alert(t('dev_hello.buttonAlert'))}
					title={t('dev_hello.buttonAlert')}
				/>
			),
		});
	}, [navigation, t, i18n.language]);

	return (
		<ThemedView style={styles.container}>
			<ThemedText type='title'>{t(message)}</ThemedText>

			<CustomButton
				variant='primary'
				onPress={() => dispatch(setMessage('dev_hello.happyDay'))}
			>
				{t('dev_hello.sendMessage')}
			</CustomButton>

			<CustomButton variant='secondary' onPress={() => dispatch(resetMessage())}>
				{t('dev_hello.reset')}
			</CustomButton>
		</ThemedView>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		paddingHorizontal: 24,
		gap: 10,
	},
});
