/**
 * Authentication Guard Component
 *
 * Redirects to login if user is not authenticated
 */

import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { router } from 'expo-router';
import { selectIsAuthenticated, selectIsLoading } from '@/features/user/userSlice';
import { ActivityIndicator, View, StyleSheet } from 'react-native';

interface AuthGuardProps {
	children: React.ReactNode;
}

export default function AuthGuard({ children }: AuthGuardProps) {
	const isAuthenticated = useSelector(selectIsAuthenticated);
	const isLoading = useSelector(selectIsLoading);

	useEffect(() => {
		if (!isLoading && !isAuthenticated) {
			router.replace('/login');
		}
	}, [isAuthenticated, isLoading]);

	if (isLoading) {
		return (
			<View style={styles.loadingContainer}>
				<ActivityIndicator size='large' color='#3b82f6' />
			</View>
		);
	}

	if (!isAuthenticated) {
		return null; // Will redirect to login
	}

	return <>{children}</>;
}

const styles = StyleSheet.create({
	loadingContainer: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		backgroundColor: '#f8f9fa',
	},
});
