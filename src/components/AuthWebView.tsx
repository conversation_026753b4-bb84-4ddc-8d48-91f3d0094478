import React, { Mu<PERSON>RefObject } from 'react';
import * as SecureStore from 'expo-secure-store';
import { AuthService } from '@/services/authService';
import { ActivityIndicator, View } from 'react-native';
import { WebView, WebViewNavigation } from 'react-native-webview';

export type AuthServiceRef = MutableRefObject<any>;

export default function AuthWebView({
  loginUrl,
  authServiceRef: _authServiceRef,
  onClose,
}: {
  loginUrl: string;
  authServiceRef: AuthServiceRef;
  onClose: () => void;
}) {
  const webViewRef = React.useRef<any>(null);
  const handledRef = React.useRef(false);

  const onAuthSuccess = async (result: { token?: string; user?: any }) => {
    console.log('Auth success:', result);
    try {
      if (result?.token) {
        await SecureStore.setItemAsync(AuthService.TOKEN_KEY, result.token);
        if (result.user !== undefined) {
          await SecureStore.setItemAsync(
            AuthService.USER_KEY,
            typeof result.user === 'string' ? result.user : JSON.stringify(result.user)
          );
        }
      }
    } catch (e) {
      if (__DEV__) console.warn('[AuthWebView] Failed to persist auth to SecureStore', e);
    } finally {
      if (!handledRef.current) {
        handledRef.current = true;
        onClose();
      }
    }
  };

  const onAuthError = (error: string) => {
    console.error('Auth error:', error);
    if (!handledRef.current) {
      handledRef.current = true;
      onClose();
    }
  };

  const onShouldStartLoadWithRequest = (request) => {
    console.log('=== Should Start Load ===');
    console.log('URL:', request.url);
    console.log('Method:', request.method);
    console.log('========================');
    
    // Handle app deep link (works for exp:// in Expo and custom scheme in prod)
    if (
      request?.url &&
      typeof AuthService?.AUTH_SUCCESS_SCHEME === 'string' &&
      AuthService.AUTH_SUCCESS_SCHEME &&
      request.url.startsWith(AuthService.AUTH_SUCCESS_SCHEME)
    ) {
      if (__DEV__) console.log('[AuthWebView] Intercepted app deep link; processing via authService');
      (async () => {
        try {
          const url: string = request.url;
          const service = _authServiceRef?.current;
          if (service && typeof service.handleWebViewNavigation === 'function') {
            const { handled, result } = await service.handleWebViewNavigation(url);
            if (handled) {
              if (__DEV__) console.log('[AuthWebView] Deep link handled by auth service', result);
              onAuthSuccess(result || {});
              return;
            }
          }
          // Not handled by service; fall back to closing
          onAuthError('Deep link not handled');
        } catch (e: any) {
          onAuthError(e?.message || 'Deep link processing error');
        }
      })();
      return false; // don't navigate to deep link inside WebView
    }

    // Intercept the callback URL: try service first, but allow navigation so injected JS can scrape JSON
    if (request?.url && request.url.includes('/sso-auth/callback')) {
      // If middleware returned an error param, fail fast and prevent redirect loops
      try {
        const u = new URL(request.url);
        const err = u.searchParams.get('error');
        if (err) {
          if (__DEV__) console.warn('[AuthWebView] Middleware callback error:', err);
          onAuthError(decodeURIComponent(err));
          return false; // stop navigation to avoid redirect loops
        }
      } catch {}

      if (__DEV__) console.log('[AuthWebView] Intercepted callback URL; trying service, allowing navigation for scrape fallback');
      // Kick off async handling without blocking the return path; if not handled, injected JS will scrape
      (async () => {
        try {
          const url: string = request.url;
          const service = _authServiceRef?.current;
          if (service && typeof service.handleWebViewNavigation === 'function') {
            const { handled, result } = await service.handleWebViewNavigation(url);
            if (handled) {
              if (__DEV__) console.log('[AuthWebView] Auth service handled callback', result);
              onAuthSuccess(result);
              return;
            }
          }
        } catch (e) {
          if (__DEV__) console.warn('[AuthWebView] Service handling error; falling back to scrape');
        }
      })();
      return true; // allow navigation so our injected JS can scrape any JSON response
    }
    
    return true;
  };
  const handleNavigationStateChange = (navState: WebViewNavigation) => {
    const { url, loading } = navState;
    console.log('=== Navigation State Change ===');
    console.log('URL:', url);
    console.log('Loading:', loading);
    console.log('================================');

    // When the callback URL finishes loading, check for JSON response
    if (url?.includes('/sso-auth/callback') && !loading) {
      console.log('🎯 SSO Callback completed - checking for JSON response');
      handleCallbackResponse();
      // Fallback: retry once more shortly in case DOM isn't ready yet
      setTimeout(() => {
        if (!handledRef.current) handleCallbackResponse();
      }, 250);
    }
  };

  const handleCallbackResponse = async () => {
    try {
      // The WebView has already made the request, but we need to get the response
      // We can inject JavaScript to get the page content
      const script = `
        (function() {
          try {
            function extractJson(str){
              if (!str) return null;
              // Remove potential BOM and trim
              try { str = str.replace(/^\uFEFF/, '').trim(); } catch(_) {}
              var match = str.match(/\{[\s\S]*\}/);
              return match ? match[0] : null;
            }
            var pre = document.querySelector('pre');
            var preText = pre ? (pre.innerText || pre.textContent) : '';
            var bodyText = (document.body && (document.body.innerText || document.body.textContent)) || '';
            var htmlText = (document.documentElement && document.documentElement.textContent) || '';
            var payload = extractJson(preText) || extractJson(bodyText) || extractJson(htmlText);
            return payload;
          } catch (e) {
            return null;
          }
        })();
      `;

      // Inject the script to get the JSON response
      webViewRef.current?.injectJavaScript(`
        (function(){
          try {
            var result = (${script});
            var msg = JSON.stringify({ payload: result });
            if (window.ReactNativeWebView && window.ReactNativeWebView.postMessage) {
              window.ReactNativeWebView.postMessage(msg);
            }
          } catch (e) {
            try {
              window.ReactNativeWebView.postMessage(JSON.stringify({ error: String(e) }));
            } catch (_) {}
          }
        })();
        true;
      `);

      // Auto-close fallback: if we didn't handle within a short window, close the WebView
      // This improves UX when the JSON is rendered but postMessage is blocked/missed
      // Also poll for a token written by other flows for a short window
      const pollStart = Date.now();
      const poll = async () => {
        if (handledRef.current) return;
        try {
          const token = await SecureStore.getItemAsync(AuthService.TOKEN_KEY);
          if (token) {
            if (__DEV__) console.log('[AuthWebView] Detected token in SecureStore after callback');
            onAuthSuccess({ token });
            return;
          }
        } catch {}
        if (Date.now() - pollStart < 2000) {
          setTimeout(poll, 250);
        } else {
          if (!handledRef.current) {
            if (__DEV__) console.log('[AuthWebView] Auto-closing fallback after callback render (no token detected)');
            onAuthError('Authentication failed');
          }
        }
      };
      setTimeout(poll, 250);
    } catch (error) {
      console.error('Error handling callback response:', error);
      onAuthError('Failed to process authentication response');
    }
  };

  const onMessage = (event: any) => {
    try {
      const raw = event?.nativeEvent?.data;
      if (__DEV__) console.log('WebView message raw:', raw);

      if (!raw) return;

      // Try wrapper first
      let parsed: any = null;
      try { parsed = JSON.parse(raw); } catch { /* not wrapper */ }

      let payload = parsed && typeof parsed === 'object' ? parsed.payload : null;
      const error = parsed && typeof parsed === 'object' ? parsed.error : null;

      if (!payload && typeof raw === 'string' && raw.trim().startsWith('{')) {
        // Raw string contained JSON directly
        payload = raw;
      }

      if (error) {
        if (__DEV__) console.warn('WebView reported error:', error);
        onAuthError(String(error));
        return;
      }

      if (payload && payload !== 'null') {
        let authResponse: any = null;
        try { authResponse = JSON.parse(payload); } catch (e) {
          if (__DEV__) console.warn('Failed to parse payload JSON:', e);
        }

        if (authResponse && typeof authResponse === 'object') {
          if (authResponse.success) {
            if (__DEV__) console.log('🎉 Authentication successful!');
            onAuthSuccess({ token: authResponse.token, user: authResponse.user });
            return;
          }
          if (__DEV__) console.log('❌ Authentication failed:', authResponse.error);
          onAuthError(authResponse.error || 'Authentication failed');
          return;
        }
      } else {
        // No payload found; don't mark failure here. Let onLoadEnd/polling decide.
        return;
      }
    } catch (error) {
      console.error('Error parsing WebView message:', error);
    }
  };
  
  const onLoadEnd = (syntheticEvent: any) => {
    try {
      const url: string | undefined = syntheticEvent?.nativeEvent?.url;
      const loading: boolean | undefined = syntheticEvent?.nativeEvent?.loading;
      if (__DEV__) console.log('WebView onLoadEnd url:', url, 'loading:', loading);
      if (!loading && url && url.includes('/sso-auth/callback')) {
        if (!handledRef.current) handleCallbackResponse();
      }
    } catch {}
  };

  return (
    <WebView
      ref={webViewRef}
      source={{ uri: loginUrl }}
      onNavigationStateChange={handleNavigationStateChange}
      onShouldStartLoadWithRequest={onShouldStartLoadWithRequest}
      onMessage={onMessage}
      onLoadEnd={onLoadEnd}
      onError={(e: any) => {
        const desc = e?.nativeEvent?.description || 'WebView error';
        if (__DEV__) console.warn('[AuthWebView] WebView onError:', e?.nativeEvent);
        onAuthError(desc);
      }}
      startInLoadingState
      javaScriptEnabled
      domStorageEnabled
      injectedJavaScriptBeforeContentLoaded={`(function(){try{if(location && location.href && location.href.indexOf('/sso-auth/callback')>-1){var t=(function(){try{function e(t){if(!t)return null;try{t=t.replace(/^\uFEFF/, '').trim()}catch(t){}var e=t.match(/\{[\s\S]*\}/);return e?e[0]:null}var n=document.querySelector('pre'),r=n?(n.innerText||n.textContent):'',o=(document.body&&(document.body.innerText||document.body.textContent))||'',a=(document.documentElement&&document.documentElement.textContent)||'';return e(r)||e(o)||e(a)}catch(t){return null}})();if(window.ReactNativeWebView&&window.ReactNativeWebView.postMessage){window.ReactNativeWebView.postMessage(JSON.stringify({payload:t}))}}}catch(t){}})();true;`}
      renderLoading={() => (
        <View style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
          <ActivityIndicator />
        </View>
      )}
    />
  );
}
