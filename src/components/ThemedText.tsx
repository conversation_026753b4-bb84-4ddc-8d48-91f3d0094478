import { StyleSheet, Text, type TextProps } from 'react-native';
import { useTheme } from '@/theme';
export type ThemedTextProps = TextProps & {
	lightColor?: string;
	darkColor?: string;
	type?: 'default' | 'title' | 'defaultSemiBold' | 'subtitle' | 'link' | 'buttonCaption';
};

export function ThemedText({
	style,
	lightColor,
	darkColor,
	type = 'default',
	...rest
}: ThemedTextProps) {
	const { colors } = useTheme();
	const typeColors: Record<NonNullable<ThemedTextProps['type']>, string> = {
		default: colors.text,
		title: colors.text,
		defaultSemiBold: colors.text,
		subtitle: colors.text,
		buttonCaption: colors.background,
		link: colors.tint ?? colors.primary, // fallback, jeśli nie masz tint
	};

	return (
		<Text
			style={[
				{ color: typeColors[type] },
				type === 'default' ? styles.default : undefined,
				type === 'title' ? styles.title : undefined,
				type === 'defaultSemiBold' ? styles.defaultSemiBold : undefined,
				type === 'subtitle' ? styles.subtitle : undefined,
				type === 'link' ? styles.link : undefined,
				type === 'buttonCaption' ? styles.buttonCaption : undefined,
				style,
			]}
			{...rest}
		/>
	);
}

const styles = StyleSheet.create({
	default: {
		fontSize: 16,
		lineHeight: 24,
	},
	defaultSemiBold: {
		fontSize: 16,
		lineHeight: 24,
		fontWeight: '600',
	},
	title: {
		fontSize: 32,
		fontWeight: 'bold',
		lineHeight: 32,
	},
	subtitle: {
		fontSize: 20,
		fontWeight: 'bold',
	},
	buttonCaption: {
		fontSize: 16,
		fontWeight: '600',
		textAlign: 'center',
	},
	link: {
		lineHeight: 30,
		fontSize: 16,
		color: '#0a7ea4',
	},
});
