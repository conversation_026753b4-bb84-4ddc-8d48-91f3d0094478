import { Text, TextProps } from 'react-native';
import React from 'react';

const variantStyles = {
	default: 'rounded',
	primary: 'color-blue-500 text-white',
	secondary: 'color-white-500 text-black',
};

type CustomTextFieldProps = TextProps & {
	variant?: keyof typeof variantStyles;
	className?: string;
};

export default function CustomTextField({
	variant = 'default',
	className = '',
	...props
}: CustomTextFieldProps) {
	return (
		<Text
			className={`
        ${variantStyles.default}
        ${variantStyles[variant]}
        ${className}
      `}
			{...props}
		/>
	);
}
