import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface HelloState {
	message: string;
}

const initialState: HelloState = {
	message: 'dev_hello.defaultMessage',
};

const helloSlice = createSlice({
	name: 'hello',
	initialState,
	reducers: {
		setMessage: (state, action: PayloadAction<string>) => {
			state.message = action.payload;
		},
		resetMessage: (state) => {
			state.message = 'dev_hello.defaultMessage';
		},
	},
});

export const { setMessage, resetMessage } = helloSlice.actions;
export default helloSlice.reducer;
