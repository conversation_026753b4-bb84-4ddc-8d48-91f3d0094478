/**
 * User Redux Slice
 *
 * Manages user authentication state and profile data
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { UserState, UnaUserProfile, LoginCredentials } from '@/types/user';
import { createUnaAuthService } from '@/services/unaAuthService';

// Initial state
const initialState: UserState = {
	isAuthenticated: false,
	isLoading: false,
	user: null,
	error: null,
};

// Async thunks
export const loginUser = createAsyncThunk(
	'user/login',
	async (credentials: LoginCredentials, { rejectWithValue }) => {
		try {
			const authService = await createUnaAuthService();
			const userProfile = await authService.login(credentials.email);
			return userProfile;
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : 'Login failed';
			return rejectWithValue(errorMessage);
		}
	},
);

export const verifyEmail = createAsyncThunk(
	'user/verifyEmail',
	async (email: string, { rejectWithValue }) => {
		try {
			const authService = await createUnaAuthService();
			const userIds = await authService.verifyEmailExists(email);
			return !!userIds;
		} catch (error) {
			const errorMessage =
				error instanceof Error ? error.message : 'Email verification failed';
			return rejectWithValue(errorMessage);
		}
	},
);

// User slice
const userSlice = createSlice({
	name: 'user',
	initialState,
	reducers: {
		logout: (state) => {
			state.isAuthenticated = false;
			state.user = null;
			state.error = null;
		},
		clearError: (state) => {
			state.error = null;
		},
		setUser: (state, action: PayloadAction<UnaUserProfile>) => {
			console.log('👤 User data updated in Redux store:', action.payload);
			state.user = action.payload;
			state.isAuthenticated = true;
			state.error = null;
		},
	},
	extraReducers: (builder) => {
		// Login user
		builder
			.addCase(loginUser.pending, (state) => {
				state.isLoading = true;
				state.error = null;
			})
			.addCase(loginUser.fulfilled, (state, action) => {
				console.log(
					'🔐 Login successful - User data saved to Redux store:',
					action.payload,
				);
				state.isLoading = false;
				state.isAuthenticated = true;
				state.user = action.payload;
				state.error = null;
			})
			.addCase(loginUser.rejected, (state, action) => {
				state.isLoading = false;
				state.isAuthenticated = false;
				state.user = null;
				state.error = action.payload as string;
			});

		// Verify email
		builder
			.addCase(verifyEmail.pending, (state) => {
				state.isLoading = true;
				state.error = null;
			})
			.addCase(verifyEmail.fulfilled, (state) => {
				state.isLoading = false;
				state.error = null;
			})
			.addCase(verifyEmail.rejected, (state, action) => {
				state.isLoading = false;
				state.error = action.payload as string;
			});
	},
});

export const { logout, clearError, setUser } = userSlice.actions;
export default userSlice.reducer;

// Selectors
export const selectUser = (state: { user: UserState }) => state.user.user;
export const selectIsAuthenticated = (state: { user: UserState }) => state.user.isAuthenticated;
export const selectIsLoading = (state: { user: UserState }) => state.user.isLoading;
export const selectError = (state: { user: UserState }) => state.user.error;
