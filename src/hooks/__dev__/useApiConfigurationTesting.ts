/**
 * API Configuration Testing Hook (Development Only)
 *
 * Custom hook for managing API configuration testing state and operations.
 * This file should NOT be included in production builds.
 *
 * AI-generated; for use in Api Keys test tab; can probably be removed
 */

import { useState } from 'react';
import { Alert } from 'react-native';
import {
	manageApiConfigurationTestingService,
	type ConfigurationTestResults,
} from '@/services/__dev__/manageApiConfigurationTesting';
import { apiConfigTestingService } from '@/services/__dev__/apiConfigTestingService';
import { apiTestingService } from '@/services/__dev__/apiTestingService';
import type { DevConfig } from '@/services/apiConfigService';

export interface UseApiConfigurationTestingReturn {
	// State
	testResults: ConfigurationTestResults;
	apiTestResponse: string;
	isLoading: boolean;

	// Test Actions
	importDevConfig: (forceReload?: boolean) => Promise<DevConfig>;
	testRetrieveKey: (expectedKey: string | null) => Promise<void>;
	testRetrieveUrl: (expectedUrl: string | null) => Promise<void>;
	testKeyExists: (expectedExists: boolean) => Promise<void>;
	testApiCall: (apiKey: string, apiBaseUrl: string) => Promise<void>;
	testFactoryPattern: (apiKey: string, apiBaseUrl: string) => Promise<void>;
	runAllTests: (apiKey: string | null, apiBaseUrl: string | null) => Promise<void>;

	// Utilities
	getTestStatusIcon: (result: boolean | null) => string;
	resetTestResults: () => void;
}

/**
 * Custom hook for API configuration testing (Development Only)
 */
export function useApiConfigurationTesting(): UseApiConfigurationTestingReturn {
	// State
	const [testResults, setTestResults] = useState<ConfigurationTestResults>({
		save: null,
		retrieve: null,
		delete: null,
		exists: null,
		saveUrl: null,
		retrieveUrl: null,
		apiTest: null,
	});
	const [apiTestResponse, setApiTestResponse] = useState<string>('');
	const [isLoading, setIsLoading] = useState(false);

	// Test Actions
	const importDevConfig = async (forceReload = false) => {
		try {
			setIsLoading(true);
			console.log('🔧 [iOS Debug] Starting dev config load, forceReload:', forceReload);

			const result =
				await manageApiConfigurationTestingService.importDevConfiguration(forceReload);
			console.log('🔧 [iOS Debug] Dev config result:', result);

			if (result.success) {
				Alert.alert('✅ Dev Config Loaded', result.message, [{ text: 'OK' }]);
				return result.config || { DEV_UNA_API_KEY: null, DEV_UNA_API_BASE_URL: null };
			} else {
				console.log('ℹ️ [iOS Debug] Dev config not available:', result.message);
				Alert.alert('ℹ️ Dev Config Info', result.message, [
					{ text: 'OK' },
					{
						text: 'Show Details',
						onPress: () => {
							console.log(
								'🔧 [iOS Debug] Full result object:',
								JSON.stringify(result, null, 2),
							);
						},
					},
				]);
				return { DEV_UNA_API_KEY: null, DEV_UNA_API_BASE_URL: null };
			}
		} catch (error) {
			console.error('❌ [iOS Debug] Failed to load dev config:', error);
			Alert.alert('❌ Dev Config Error', `Failed to load dev config: ${error}`, [
				{ text: 'OK' },
			]);
			return { DEV_UNA_API_KEY: null, DEV_UNA_API_BASE_URL: null };
		} finally {
			setIsLoading(false);
		}
	};

	const testRetrieveKey = async (expectedKey: string | null) => {
		setIsLoading(true);
		try {
			// Simple test operation - use testing service directly
			const result = await apiConfigTestingService.testRetrieveApiKey();
			const isSuccess = result.success && result.retrievedKey === expectedKey;
			setTestResults((prev) => ({ ...prev, retrieve: isSuccess }));

			if (isSuccess) {
				Alert.alert(
					'Test Passed',
					`Retrieved API key: ${result.retrievedKey?.substring(0, 10)}...`,
				);
			} else {
				Alert.alert(
					'Test Failed',
					result.error || 'Retrieved API key does not match stored value',
				);
			}
		} catch (error: any) {
			setTestResults((prev) => ({ ...prev, retrieve: false }));
			Alert.alert('Test Failed', `Failed to retrieve API key: ${error.message}`);
		} finally {
			setIsLoading(false);
		}
	};

	const testRetrieveUrl = async (expectedUrl: string | null) => {
		setIsLoading(true);
		try {
			// Simple test operation - use testing service directly
			const result = await apiConfigTestingService.testRetrieveApiBaseUrl();
			const isSuccess = result.success && result.retrievedUrl === expectedUrl;
			setTestResults((prev) => ({ ...prev, retrieveUrl: isSuccess }));

			if (isSuccess) {
				Alert.alert('Test Passed', `Retrieved API base URL: ${result.retrievedUrl}`);
			} else {
				Alert.alert(
					'Test Failed',
					result.error || 'Retrieved API base URL does not match stored value',
				);
			}
		} catch (error: any) {
			setTestResults((prev) => ({ ...prev, retrieveUrl: false }));
			Alert.alert('Test Failed', `Failed to retrieve API base URL: ${error.message}`);
		} finally {
			setIsLoading(false);
		}
	};

	const testKeyExists = async (expectedExists: boolean) => {
		setIsLoading(true);
		try {
			// Simple test operation - use testing service directly
			const result = await apiConfigTestingService.testApiKeyExists();
			const isSuccess = result.success && result.exists === expectedExists;
			setTestResults((prev) => ({ ...prev, exists: isSuccess }));

			if (isSuccess) {
				Alert.alert(
					'Test Passed',
					`Key existence check passed: ${result.exists ? 'exists' : 'does not exist'}`,
				);
			} else {
				Alert.alert(
					'Test Failed',
					result.error || `Expected: ${expectedExists}, Got: ${result.exists}`,
				);
			}
		} catch (error: any) {
			setTestResults((prev) => ({ ...prev, exists: false }));
			Alert.alert('Test Failed', `Failed to check key existence: ${error.message}`);
		} finally {
			setIsLoading(false);
		}
	};

	const testApiCall = async (apiKey: string, apiBaseUrl: string) => {
		if (!apiKey || !apiBaseUrl) {
			Alert.alert('Error', 'Please save both API key and base URL first');
			return;
		}

		setIsLoading(true);
		setApiTestResponse('Testing...');

		try {
			// Simple API test - use service directly
			const result = await apiTestingService.testApiCall(apiKey, apiBaseUrl);
			setTestResults((prev) => ({ ...prev, apiTest: result.success }));

			if (result.success) {
				const responseText = `Status: ${result.status}\n\nResponse:\n${JSON.stringify(result.data, null, 2)}`;
				setApiTestResponse(responseText);
				Alert.alert('API Test Passed', 'API call successful! Check the response below.');
			} else {
				const errorText = result.status
					? `Status: ${result.status}\n\nResponse:\n${JSON.stringify(result.data, null, 2)}`
					: `Error: ${result.error}`;
				setApiTestResponse(errorText);
				Alert.alert('API Test Failed', result.error || 'Unknown error occurred');
			}
		} catch (error: any) {
			setTestResults((prev) => ({ ...prev, apiTest: false }));
			setApiTestResponse(`Error: ${error.message}`);
			Alert.alert('API Test Failed', error.message);
		} finally {
			setIsLoading(false);
		}
	};

	const testFactoryPattern = async (apiKey: string, apiBaseUrl: string) => {
		if (!apiKey || !apiBaseUrl) {
			Alert.alert('Error', 'Please save both API key and base URL first');
			return;
		}

		setIsLoading(true);
		setApiTestResponse('Testing with API Factory...');

		try {
			// Factory pattern demonstration - use service directly
			const result = await apiTestingService.testFactoryPattern(apiKey, apiBaseUrl);
			setTestResults((prev) => ({ ...prev, apiTest: result.success }));

			if (result.success) {
				const responseText = `API Factory Pattern Demo - Success!\n\nFactory Benefits:\n- Dependency injection ✅\n- Configurable options ✅\n- Consistent interface ✅\n- Centralized client creation ✅\n\nAPI Response (Status: ${result.status}):\n${JSON.stringify(result.data, null, 2)}`;
				setApiTestResponse(responseText);
				Alert.alert(
					'API Factory Demo Passed',
					'Factory pattern demonstrated successfully! Check the response below.',
				);
			} else {
				const errorText = `API Factory Demo - Error:\nStatus: ${result.status || 'N/A'}\nError: ${result.error}`;
				setApiTestResponse(errorText);
				Alert.alert('API Factory Demo Failed', result.error || 'Unknown error occurred');
			}
		} catch (error: any) {
			setTestResults((prev) => ({ ...prev, apiTest: false }));
			setApiTestResponse(`API Factory Demo - Error:\n${error.message}`);
			Alert.alert('API Factory Demo Failed', error.message);
		} finally {
			setIsLoading(false);
		}
	};

	const runAllTests = async (apiKey: string | null, apiBaseUrl: string | null) => {
		setIsLoading(true);

		// Reset test results
		resetTestResults();
		setApiTestResponse('');

		try {
			const result = await manageApiConfigurationTestingService.runTestSuite(
				apiKey,
				apiBaseUrl,
			);
			setTestResults(result.results);

			if (result.apiResponse) {
				setApiTestResponse(result.apiResponse);
			}

			Alert.alert(
				result.allPassed ? 'All Tests Passed!' : 'Some Tests Failed',
				result.summary,
			);
		} catch (error: any) {
			Alert.alert('Error', `Test suite failed: ${error.message}`);
		} finally {
			setIsLoading(false);
		}
	};

	const getTestStatusIcon = (result: boolean | null) => {
		if (result === null) return '⏳';
		return result ? '✅' : '❌';
	};

	const resetTestResults = () => {
		setTestResults({
			save: null,
			retrieve: null,
			delete: null,
			exists: null,
			saveUrl: null,
			retrieveUrl: null,
			apiTest: null,
		});
	};

	return {
		// State
		testResults,
		apiTestResponse,
		isLoading,

		// Test Actions
		importDevConfig,
		testRetrieveKey,
		testRetrieveUrl,
		testKeyExists,
		testApiCall,
		testFactoryPattern,
		runAllTests,

		// Utilities
		getTestStatusIcon,
		resetTestResults,
	};
}
