/**
 * Development Configuration Service (Development Only)
 *
 * Handles loading and managing development-specific configuration.
 * This file should NOT be included in production builds.
 *
 */

import { apiConfigService, type DevConfig } from '../apiConfigService';

export interface DevConfigLoadResult {
	success: boolean;
	message: string;
	config?: DevConfig;
}

/**
 * Service for development configuration operations (Development Only)
 */
export class DevConfigService {
	/**
	 * Load development configuration from dev.config.json
	 */
	async importDevConfiguration(forceReload = false): Promise<DevConfigLoadResult> {
		try {
			return await apiConfigService.importDevConfig(forceReload);
		} catch (error) {
			return {
				success: false,
				message: `Failed to load development configuration: ${error}`,
			};
		}
	}

	/**
	 * Test if development configuration is available
	 */
	async isDevConfigAvailable(): Promise<boolean> {
		try {
			const result = await this.importDevConfiguration();
			return result.success && !!result.config;
		} catch {
			return false;
		}
	}

	/**
	 * Get development configuration values
	 */
	async getDevConfigValues(): Promise<{
		apiKey: string | null;
		apiBaseUrl: string | null;
	}> {
		try {
			const result = await this.importDevConfiguration();
			if (result.success && result.config) {
				return {
					apiKey: result.config.DEV_UNA_API_KEY || null,
					apiBaseUrl: result.config.DEV_UNA_API_BASE_URL || null,
				};
			}
		} catch (error) {
			console.error('Failed to get dev config values:', error);
		}

		return {
			apiKey: null,
			apiBaseUrl: null,
		};
	}
}

// Export singleton instance for development use
export const devConfigService = new DevConfigService();
