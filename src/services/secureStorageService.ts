/**
 * Secure Storage Service (SHARED INFRASTRUCTURE)
 *
 * Business logic layer for secure storage operations.
 * Handles domain-specific operations, validation, and complex workflows.
 *
 * 🔄 SHARED BETWEEN UNA AND OKTA IMPLEMENTATIONS
 * - Used by both UNA authentication and Okta POC
 * - Storage keys are namespaced to prevent conflicts
 * - UNA uses: test_api_key, api_base_url
 * - <PERSON><PERSON> uses: okta_* prefixed keys
 * - Safe to enhance, but don't break existing UNA functionality
 */

import { AuthData, secureStorageRepository } from '@/repositories/secureStorageRepository';
import {
	ValidationError,
	AuthDataValidators,
	StorageValidators,
	TokenUpdateValidators,
} from '@/utils/validation';

/**
 * Service for secure storage business operations
 */
export class SecureStorageService {
	/**
	 * Store authentication data with validation
	 */
	async saveAuthData(authData: AuthData): Promise<void> {
		try {
			// Validate complete auth data using centralized validator
			AuthDataValidators.authData(authData);

			// Save to repository
			await secureStorageRepository.saveAuthData(authData);
		} catch (error) {
			if (error instanceof ValidationError) {
				throw error; // Re-throw validation errors as-is
			}
			throw new Error(`Failed to save authentication data: ${error}`);
		}
	}

	/**
	 * Retrieve authentication data
	 */
	async getAuthData(): Promise<AuthData | null> {
		try {
			return await secureStorageRepository.getAuthData();
		} catch (error) {
			console.error('Failed to retrieve authentication data:', error);
			return null;
		}
	}

	/**
	 * Clear authentication data
	 */
	async clearAuthData(): Promise<void> {
		try {
			await secureStorageRepository.clearAuthData();
		} catch (error) {
			throw new Error(`Failed to clear authentication data: ${error}`);
		}
	}

	/**
	 * Check if user is authenticated (business logic)
	 */
	async isAuthenticated(): Promise<boolean> {
		try {
			const authData = await this.getAuthData();
			if (!authData) {
				return false;
			}

			// Business logic: Check if token is expired
			const now = Date.now();
			return authData.expiresAt > now;
		} catch (error) {
			console.error('Failed to check authentication status:', error);
			return false;
		}
	}

	/**
	 * Refresh authentication if needed (business logic)
	 */
	async refreshAuthIfNeeded(): Promise<boolean> {
		try {
			const authData = await this.getAuthData();
			if (!authData) {
				return false;
			}

			// Business logic: Check if token expires within 5 minutes
			const fiveMinutesFromNow = Date.now() + 5 * 60 * 1000;
			const needsRefresh = authData.expiresAt <= fiveMinutesFromNow;

			if (needsRefresh) {
				// In a real app, this would call an API to refresh the token
				console.log('Token needs refresh - implement refresh logic here');
				return false;
			}

			return true;
		} catch (error) {
			console.error('Failed to check if auth refresh is needed:', error);
			return false;
		}
	}

	/**
	 * Store generic secure data with validation
	 */
	async storeSecureData<T>(key: string, data: T): Promise<void> {
		try {
			// Validate inputs using centralized validators
			StorageValidators.key(key);
			StorageValidators.data(data);

			await secureStorageRepository.setObject(key, data);
		} catch (error) {
			if (error instanceof ValidationError) {
				throw error; // Re-throw validation errors as-is
			}
			throw new Error(`Failed to store secure data for key ${key}: ${error}`);
		}
	}

	/**
	 * Retrieve generic secure data
	 */
	async getSecureData<T>(key: string): Promise<T | null> {
		try {
			// Validate key using centralized validator
			StorageValidators.key(key);

			return await secureStorageRepository.getObject<T>(key);
		} catch (error) {
			if (error instanceof ValidationError) {
				throw error; // Re-throw validation errors as-is
			}
			console.error(`Failed to retrieve secure data for key ${key}:`, error);
			return null;
		}
	}

	/**
	 * Remove specific secure data
	 */
	async removeSecureData(key: string): Promise<void> {
		try {
			// Validate key using centralized validator
			StorageValidators.key(key);

			await secureStorageRepository.removeItem(key);
		} catch (error) {
			if (error instanceof ValidationError) {
				throw error; // Re-throw validation errors as-is
			}
			throw new Error(`Failed to remove secure data for key ${key}: ${error}`);
		}
	}

	/**
	 * Remove secure object data with validation
	 */
	async removeSecureObject(key: string): Promise<void> {
		try {
			// Validate key using centralized validator
			StorageValidators.key(key);

			await secureStorageRepository.removeObject(key);
		} catch (error) {
			if (error instanceof ValidationError) {
				throw error; // Re-throw validation errors as-is
			}
			throw new Error(`Failed to remove secure object for key ${key}: ${error}`);
		}
	}

	/**
	 * Clear all user data (business workflow)
	 */
	async clearAllUserData(): Promise<void> {
		try {
			await secureStorageRepository.clearAllUserData();
		} catch (error) {
			throw new Error(`Failed to logout: ${error}`);
		}
	}

	/**
	 * Check if user has any stored data (business logic)
	 */
	async hasUserData(): Promise<boolean> {
		try {
			const authData = await this.getAuthData();
			return authData !== null;
		} catch (error) {
			console.error('Failed to check if user has data:', error);
			return false;
		}
	}

	/**
	 * Get access token for API calls (Okta-specific)
	 */
	async getAccessToken(): Promise<string | null> {
		try {
			const authData = await this.getAuthData();
			if (!authData || !authData.token) {
				return null;
			}

			// Check if token is expired
			if (authData.expiresAt <= Date.now()) {
				console.warn('Access token is expired');
				return null;
			}

			return authData.token;
		} catch (error) {
			console.error('Failed to get access token:', error);
			return null;
		}
	}

	/**
	 * Get user profile from stored auth data (Okta-specific)
	 */
	async getUserProfile(): Promise<AuthData['userProfile'] | null> {
		try {
			const authData = await this.getAuthData();
			return authData?.userProfile || null;
		} catch (error) {
			console.error('Failed to get user profile:', error);
			return null;
		}
	}

	/**
	 * Check if tokens need refresh (expires within 5 minutes)
	 */
	async needsTokenRefresh(): Promise<boolean> {
		try {
			const authData = await this.getAuthData();
			if (!authData || !authData.token) {
				return false;
			}

			const fiveMinutesFromNow = Date.now() + 5 * 60 * 1000;
			return authData.expiresAt <= fiveMinutesFromNow;
		} catch (error) {
			console.error('Failed to check if token needs refresh:', error);
			return false;
		}
	}

	/**
	 * Update tokens after refresh (Okta-specific)
	 */
	async updateTokens(
		accessToken: string,
		refreshToken?: string,
		expiresIn?: number,
	): Promise<void> {
		try {
			// Validate input parameters using centralized validators
			TokenUpdateValidators.accessToken(accessToken);
			TokenUpdateValidators.refreshToken(refreshToken);
			TokenUpdateValidators.expiresIn(expiresIn);

			const existingAuthData = await this.getAuthData();
			if (!existingAuthData) {
				throw new Error('No existing authentication data to update');
			}

			const updatedAuthData: AuthData = {
				...existingAuthData,
				token: accessToken,
				refreshToken: refreshToken || existingAuthData.refreshToken,
				expiresAt: expiresIn ? Date.now() + expiresIn * 1000 : existingAuthData.expiresAt,
			};

			await this.saveAuthData(updatedAuthData);
		} catch (error) {
			if (error instanceof ValidationError) {
				throw error; // Re-throw validation errors as-is
			}
			throw new Error(`Failed to update tokens: ${error}`);
		}
	}
}

// Note: No singleton export to reduce unused API surface.
