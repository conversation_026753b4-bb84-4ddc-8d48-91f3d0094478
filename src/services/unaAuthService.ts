/**
 * UNA Authentication Service
 *
 * Handles email verification and user profile retrieval from UNA CMS API
 * Uses direct fetch approach for better compatibility with UNA API
 */

import { UnaUserIds, UnaUserProfile } from '@/types/user';

export class UnaAuthService {
	private apiKey: string;
	private baseUrl: string;

	constructor(apiKey: string, baseUrl: string) {
		this.apiKey = apiKey;
		this.baseUrl = baseUrl;
	}

	/**
	 * Verify if an email address exists in UNA database and get user IDs
	 * Returns extended user data if available
	 */
	async verifyEmailExists(
		email: string,
	): Promise<{ userIds: UnaUserIds; userData?: any } | null> {
		try {
			const response = await fetch(
				`${this.baseUrl}/api.php?r=system/get_user_ids/TemplServiceContent&params=["${email}"]`,
				{
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${this.apiKey}`,
					},
				},
			);

			if (!response.ok) {
				throw new Error(`HTTP ${response.status}: ${response.statusText}`);
			}

			const result = await response.json();
			console.log('📧 Email verification response from UNA:', result);

			// Handle the nested response structure for email verification
			if (result?.data?.account_id) {
				const userIds = {
					account_id: result.data.account_id,
					content_id: result.data.content_id,
					profile_id: result.data.profile_id,
				};
				console.log('✅ User IDs extracted from UNA:', userIds);

				return {
					userIds,
					userData: result.data, // Include any additional data from the response
				};
			}

			return null;
		} catch (error) {
			console.error('Email verification error:', error);
			const errorMessage = error instanceof Error ? error.message : 'Unknown error';
			throw new Error(`Failed to verify email: ${errorMessage}`);
		}
	}

	/**
	 * Get user profile information using user IDs and optional existing user data
	 */
	async getUserProfile(
		userIds: UnaUserIds,
		existingUserData?: any,
	): Promise<UnaUserProfile | null> {
		// Always call the profile endpoint to get complete user data
		console.log(
			'🔍 Fetching complete user profile from UNA API for account_id:',
			userIds.account_id,
		);

		try {
			// Use the correct UNA endpoint for getting user info
			const response = await fetch(
				`${this.baseUrl}/api.php?r=system/get_info/TemplServiceContent&params=["sys_account", ${userIds.account_id}]`,
				{
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						Authorization: `Bearer ${this.apiKey}`,
					},
				},
			);

			if (!response.ok) {
				console.warn(
					`Profile endpoint failed: HTTP ${response.status}: ${response.statusText}. Creating basic profile from user IDs.`,
				);
				// Create a basic profile with just the IDs and minimal data
				return this.mapToUserProfile(userIds, {});
			}

			const result = await response.json();
			console.log('👤 User profile response from UNA:', result);

			// Handle the nested response structure: {status, module, method, params, data, hash}
			if (result?.data) {
				return this.mapToUserProfile(userIds, result.data);
			}

			// Fallback to basic profile
			return this.mapToUserProfile(userIds, {});
		} catch (error) {
			console.error('User profile retrieval error:', error);
			// Don't throw error, return basic profile instead
			return this.mapToUserProfile(userIds, {});
		}
	}

	/**
	 * Map UNA data to user profile structure
	 */
	private mapToUserProfile(userIds: UnaUserIds, data: any): UnaUserProfile {
		const userProfile = {
			// Use the actual account ID from the profile response, fallback to userIds
			account_id: data.id || userIds.account_id,
			content_id: userIds.content_id,
			profile_id: data.profile_id || userIds.profile_id,
			email: data.email || '',
			username: data.name || `user_${data.id || userIds.account_id}`,
			display_name: data.name || `User ${data.id || userIds.account_id}`,
			role: data.role ? data.role.toString() : 'user',
			status: data.active ? 'active' : 'inactive',
			avatar_url: data.avatar_url || data.picture || '',
			created_at: data.added ? new Date(data.added * 1000).toISOString() : '',
			last_login: data.logged ? new Date(data.logged * 1000).toISOString() : '',
			// Additional UNA-specific fields
			email_confirmed: data.email_confirmed || 0,
			phone: data.phone || '',
			phone_confirmed: data.phone_confirmed || 0,
			receive_updates: data.receive_updates || 0,
			receive_news: data.receive_news || 0,
			lang_id: data.lang_id || 1,
			login_attempts: data.login_attempts || 0,
			locked: data.locked || 0,
			ip: data.ip || '',
			referred: data.referred || '',
			...data, // Include all additional UNA fields
		};
		console.log('🏗️ Mapped user profile for UserContext:', userProfile);
		return userProfile;
	}

	/**
	 * Complete login process: verify email and get profile
	 */
	async login(email: string): Promise<UnaUserProfile | null> {
		try {
			// Step 1: Verify email exists and get user IDs
			const emailResult = await this.verifyEmailExists(email);
			if (!emailResult) {
				throw new Error('Email address not found in database');
			}

			// Step 2: Get full user profile using IDs and any existing data
			const userProfile = await this.getUserProfile(
				emailResult.userIds,
				emailResult.userData,
			);
			if (!userProfile) {
				throw new Error('Failed to retrieve user profile');
			}

			return userProfile;
		} catch (error) {
			console.error('Login error:', error);
			throw error;
		}
	}
}

/**
 * Load dev configuration
 */
const importDevConfig = async () => {
	try {
		// Try to load actual dev config first
		const devConfig = await import('../../dev.config.json');
		return devConfig.default || devConfig;
	} catch {
		// Fall back to fallback config
		const fallbackConfig = await import('@/config/dev.config.fallback');
		return fallbackConfig.default || fallbackConfig;
	}
};

/**
 * Factory function to create UNA auth service with dev config
 */
export const createUnaAuthService = async (): Promise<UnaAuthService> => {
	const devConfig = await importDevConfig();

	if (!devConfig.DEV_UNA_API_KEY || !devConfig.DEV_UNA_API_BASE_URL) {
		throw new Error('UNA API configuration not found. Please check dev.config.json');
	}

	return new UnaAuthService(devConfig.DEV_UNA_API_KEY, devConfig.DEV_UNA_API_BASE_URL);
};
