/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

export type ColorsProps = {
	text: string;
	primary: string;
	background: string;
	card: string;
	tint: string;
	icon: string;
	tabIconDefault: string;
	tabIconSelected: string;
	border: string;
	notification: string;
};

export type ColorsConfig = {
	light: ColorsProps;
	dark: ColorsProps;
};

export const Colors: ColorsConfig = {
	light: {
		text: '#111c13ff',
		primary: 'rgba(0, 16, 238, 1)',
		background: '#fff',
		card: '#fff',
		tint: '#0a7ea4',
		icon: '#687076',
		tabIconDefault: '#687076',
		tabIconSelected: '#0a7ea4',
		border: '#111c13ff',
		notification: '#fff',
	},
	dark: {
		text: '#ECEDEE',
		primary: '#111c13ff',
		background: '#151718',
		card: '#151718',
		tint: '#0a7ea4',
		icon: '#687076',
		tabIconDefault: '#687076',
		tabIconSelected: '#0a7ea4',
		border: '#ECEDEE',
		notification: '#151718',
	},
};
