export const Tokens: TokensProps = {
	Spacing: {
		xs: 4,
		s: 8,
		m: 16,
		l: 24,
		xl: 32,
	},
	Radius: {
		xs: 4,
		s: 8,
		m: 16,
		l: 24,
		xl: 32,
	},
	Sizes: {
		xxs: 8,
		xs: 12,
		s: 16,
		m: 32,
		l: 64,
		xl: 128,
		icon: 28,
	},
};

type SizesProps = {
	xxs: number; // extra extra small
	xs: number; // extra small
	s: number; // small
	m: number; // medium
	l: number; // large
	xl: number; // extra large
	icon: number; // zamiast tabBarIconSize, bardziej uniwersalnie
};

type SpacingProps = {
	xs: number;
	s: number;
	m: number;
	l: number;
	xl: number;
};

type RadiusProps = {
	xs: number;
	s: number;
	m: number;
	l: number;
	xl: number;
};

export type TokensProps = {
	Spacing: SpacingProps;
	Radius: RadiusProps;
	Sizes: SizesProps;
};
