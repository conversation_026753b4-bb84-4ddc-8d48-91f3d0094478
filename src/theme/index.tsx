import { useTheme as useNavTheme, Theme as NavTheme } from '@react-navigation/native';
import { fonts } from './fonts';
import { Colors, ColorsProps } from '@/theme/Colors';
import { TokensProps, Tokens as tokensConfig } from '@/theme/Tokens';
export type AppTheme = Omit<NavTheme, 'colors'> & {
	colors: NavTheme['colors'] & ColorsProps;
	tokens: TokensProps;
};

export const DefaultTheme: AppTheme = {
	dark: false,
	colors: Colors.light,
	tokens: tokensConfig,
	fonts,
};

export function useTheme(): AppTheme {
	return useNavTheme<AppTheme>();
}

export { Tokens } from '@/theme/Tokens';
