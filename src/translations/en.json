{"tabs": {"home": "Home", "explore": "Explore", "apiKeys": "API Keys"}, "dev_home": {"welcome": "Welcome", "waveAgain": "Wave Again", "config": "Config", "navigation": "Navigation", "navigateButton": "Click to navigate", "apiKeysHeader": "A<PERSON> <PERSON>", "apiKeysButton": "<PERSON><PERSON>e <PERSON>"}, "dev_hello": {"title": "Hello Screen", "defaultMessage": "Hello World", "buttonAlert": "Click Me", "sendMessage": "Send Message", "reset": "Reset", "happyDay": "🎈Happy Day🎈"}, "dev_explore": {"title": "Explore", "intro": "This app includes example code to help you get started.", "fileRouting": {"title": "File-based routing", "desc1": "This app has two screens: app/(tabs)/index.tsx and app/(tabs)/explore.tsx", "desc2": "The layout file in app/(tabs)/_layout.tsx sets up the tab navigator.", "learnMore": "Learn more"}, "platformSupport": {"title": "Android, iOS, and web support", "desc": "You can open this project on Android, iOS, and the web. To open the web version, press \"w\" in the terminal running this project."}, "images": {"title": "Images", "desc": "For static images, you can use the @2x and @3x suffixes to provide files for different screen densities", "learnMore": "Learn more"}, "fonts": {"title": "Custom fonts", "desc1": "Open app/_layout.tsx to see how to load custom fonts such as this one.", "learnMore": "Learn more"}, "themes": {"title": "Light and dark mode components", "desc": "This template has light and dark mode support. The useColorScheme() hook lets you inspect what the user's current color scheme is, and so you can adjust UI colors accordingly.", "learnMore": "Learn more"}, "animations": {"title": "Animations", "desc1": "This template includes an example of an animated component. The components/HelloWave.tsx component uses the powerful react-native-reanimated library to create a waving hand animation.", "desc2": "The components/ParallaxScrollView.tsx component provides a parallax effect for the header image."}}, "common": {"learnMore": "Learn more"}}