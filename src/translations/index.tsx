import { createInstance } from 'i18next';
import { initReactI18next } from 'react-i18next';
import { useEffect, useState } from 'react';

import en from './en.json';

const i18n = createInstance();

const resources = {
	en: { translation: en },
};

export async function initI18n() {
	if (i18n.isInitialized) return i18n;

	await i18n.use(initReactI18next).init({
		resources,
		lng: 'en',
		fallbackLng: 'en',
		compatibilityJSON: 'v3',
		interpolation: { escapeValue: false },
		returnNull: false,
	});

	return i18n;
}

export function useI18n(): [boolean] {
	const [ready, setReady] = useState<boolean>(i18n.isInitialized);

	useEffect(() => {
		let mounted = true;

		if (!i18n.isInitialized) {
			initI18n().then(() => {
				if (mounted) setReady(true);
			});
		}

		return () => {
			mounted = false;
		};
	}, []);

	return [ready];
}

export async function makeTestI18n(resources: any) {
	const i18n = createInstance();
	await i18n.use(initReactI18next).init({
		resources,
		lng: 'en',
		fallbackLng: 'en',
		interpolation: { escapeValue: false },
		returnNull: false,
	});
	return i18n;
}

export default i18n;
