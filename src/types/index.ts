/**
 * Shared types for API services
 */

/**
 * Storage interface for dependency injection
 */
export interface IStorageRepository {
	getItem(key: string): Promise<string | null>;
	setItem(key: string, value: string): Promise<void>;
	removeItem(key: string): Promise<void>;
}

/**
 * API Configuration interface
 */
export interface IApiConfigService {
	loadApiConfig(): Promise<{ apiKey: string | null; apiBaseUrl: string | null }>;
	saveApiKey(apiKey: string): Promise<void>;
	saveApiBaseUrl(url: string): Promise<void>;
}

/**
 * API Client configuration options
 */
export interface ApiClientOptions {
	timeout?: number;
	retryAttempts?: number;
	baseUrl?: string; // Override config baseUrl
}

/**
 * API Response wrapper
 */
export interface ApiResponse<T = any> {
	data: T;
	success: boolean;
	message?: string;
}
