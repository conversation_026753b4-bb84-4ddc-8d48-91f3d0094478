/**
 * Tests for validation utilities
 */

import {
	ValidationError,
	CommonValidators,
	AuthDataValidators,
	StorageValidators,
	TokenUpdateValidators,
} from '../validation';
import type { AuthData } from '@/repositories/secureStorageRepository';

describe('ValidationError', () => {
	it('should create error with field and message', () => {
		const error = new ValidationError('testField', 'Test message');
		expect(error.message).toBe('testField: Test message');
		expect(error.name).toBe('ValidationError');
		expect(error.field).toBe('testField');
	});
});

describe('CommonValidators', () => {
	describe('requiredString', () => {
		it('should pass for valid string', () => {
			expect(() => CommonValidators.requiredString('valid', 'test')).not.toThrow();
		});

		it('should throw for empty string', () => {
			expect(() => CommonValidators.requiredString('', 'test')).toThrow(ValidationError);
		});

		it('should throw for null', () => {
			expect(() => CommonValidators.requiredString(null, 'test')).toThrow(ValidationError);
		});

		it('should throw for undefined', () => {
			expect(() => CommonValidators.requiredString(undefined, 'test')).toThrow(
				ValidationError,
			);
		});

		it('should throw for number', () => {
			expect(() => CommonValidators.requiredString(123, 'test')).toThrow(ValidationError);
		});
	});

	describe('stringMinLength', () => {
		it('should pass for string meeting minimum length', () => {
			expect(() => CommonValidators.stringMinLength('12345', 5, 'test')).not.toThrow();
		});

		it('should pass for string exceeding minimum length', () => {
			expect(() => CommonValidators.stringMinLength('123456', 5, 'test')).not.toThrow();
		});

		it('should throw for string below minimum length', () => {
			expect(() => CommonValidators.stringMinLength('1234', 5, 'test')).toThrow(
				ValidationError,
			);
		});
	});

	describe('email', () => {
		it('should pass for valid email', () => {
			expect(() => CommonValidators.email('<EMAIL>', 'email')).not.toThrow();
		});

		it('should throw for invalid email', () => {
			expect(() => CommonValidators.email('invalid-email', 'email')).toThrow(ValidationError);
		});

		it('should throw for email without domain', () => {
			expect(() => CommonValidators.email('user@', 'email')).toThrow(ValidationError);
		});
	});

	describe('futureTimestamp', () => {
		it('should pass for future timestamp', () => {
			const future = Date.now() + 1000;
			expect(() => CommonValidators.futureTimestamp(future, 'timestamp')).not.toThrow();
		});

		it('should throw for past timestamp', () => {
			const past = Date.now() - 1000;
			expect(() => CommonValidators.futureTimestamp(past, 'timestamp')).toThrow(
				ValidationError,
			);
		});

		it('should throw for current timestamp', () => {
			const now = Date.now();
			expect(() => CommonValidators.futureTimestamp(now, 'timestamp')).toThrow(
				ValidationError,
			);
		});
	});
});

describe('AuthDataValidators', () => {
	describe('token', () => {
		it('should pass for valid token', () => {
			expect(() => AuthDataValidators.token('valid-token-12345')).not.toThrow();
		});

		it('should throw for short token', () => {
			expect(() => AuthDataValidators.token('short')).toThrow(ValidationError);
		});

		it('should throw for empty token', () => {
			expect(() => AuthDataValidators.token('')).toThrow(ValidationError);
		});
	});

	describe('userProfile', () => {
		it('should pass for valid user profile', () => {
			const profile = {
				sub: 'user-123',
				email: '<EMAIL>',
				name: 'Test User',
			};
			expect(() => AuthDataValidators.userProfile(profile)).not.toThrow();
		});

		it('should pass for undefined profile', () => {
			expect(() => AuthDataValidators.userProfile(undefined)).not.toThrow();
		});

		it('should throw for profile without sub', () => {
			const profile = {
				email: '<EMAIL>',
				name: 'Test User',
			} as any;
			expect(() => AuthDataValidators.userProfile(profile)).toThrow(ValidationError);
		});

		it('should throw for profile with invalid email', () => {
			const profile = {
				sub: 'user-123',
				email: 'invalid-email',
				name: 'Test User',
			};
			expect(() => AuthDataValidators.userProfile(profile)).toThrow(ValidationError);
		});
	});

	describe('authData', () => {
		const validAuthData: AuthData = {
			token: 'valid-access-token-12345',
			refreshToken: 'valid-refresh-token-12345',
			expiresAt: Date.now() + 3600000,
		};

		it('should pass for valid auth data', () => {
			expect(() => AuthDataValidators.authData(validAuthData)).not.toThrow();
		});

		it('should throw for invalid token', () => {
			const invalid = { ...validAuthData, token: 'short' };
			expect(() => AuthDataValidators.authData(invalid)).toThrow(ValidationError);
		});

		it('should throw for invalid refresh token', () => {
			const invalid = { ...validAuthData, refreshToken: 'short' };
			expect(() => AuthDataValidators.authData(invalid)).toThrow(ValidationError);
		});

		it('should throw for expired token', () => {
			const invalid = { ...validAuthData, expiresAt: Date.now() - 1000 };
			expect(() => AuthDataValidators.authData(invalid)).toThrow(ValidationError);
		});
	});
});

describe('StorageValidators', () => {
	describe('key', () => {
		it('should pass for valid key', () => {
			expect(() => StorageValidators.key('valid-key')).not.toThrow();
		});

		it('should throw for empty key', () => {
			expect(() => StorageValidators.key('')).toThrow(ValidationError);
		});

		it('should throw for whitespace-only key', () => {
			expect(() => StorageValidators.key('   ')).toThrow(ValidationError);
		});
	});

	describe('data', () => {
		it('should pass for valid data', () => {
			expect(() => StorageValidators.data({ test: 'data' })).not.toThrow();
		});

		it('should pass for string data', () => {
			expect(() => StorageValidators.data('test')).not.toThrow();
		});

		it('should pass for number data', () => {
			expect(() => StorageValidators.data(123)).not.toThrow();
		});

		it('should pass for boolean data', () => {
			expect(() => StorageValidators.data(true)).not.toThrow();
		});

		it('should throw for null data', () => {
			expect(() => StorageValidators.data(null)).toThrow(ValidationError);
		});

		it('should throw for undefined data', () => {
			expect(() => StorageValidators.data(undefined)).toThrow(ValidationError);
		});
	});
});

describe('TokenUpdateValidators', () => {
	describe('accessToken', () => {
		it('should pass for valid access token', () => {
			expect(() => TokenUpdateValidators.accessToken('valid-token-12345')).not.toThrow();
		});

		it('should throw for short access token', () => {
			expect(() => TokenUpdateValidators.accessToken('short')).toThrow(ValidationError);
		});
	});

	describe('refreshToken', () => {
		it('should pass for valid refresh token', () => {
			expect(() =>
				TokenUpdateValidators.refreshToken('valid-refresh-token-12345'),
			).not.toThrow();
		});

		it('should pass for undefined refresh token', () => {
			expect(() => TokenUpdateValidators.refreshToken(undefined)).not.toThrow();
		});

		it('should throw for short refresh token', () => {
			expect(() => TokenUpdateValidators.refreshToken('short')).toThrow(ValidationError);
		});
	});

	describe('expiresIn', () => {
		it('should pass for valid expires in', () => {
			expect(() => TokenUpdateValidators.expiresIn(3600)).not.toThrow();
		});

		it('should pass for undefined expires in', () => {
			expect(() => TokenUpdateValidators.expiresIn(undefined)).not.toThrow();
		});

		it('should throw for negative expires in', () => {
			expect(() => TokenUpdateValidators.expiresIn(-100)).toThrow(ValidationError);
		});

		it('should throw for zero expires in', () => {
			expect(() => TokenUpdateValidators.expiresIn(0)).toThrow(ValidationError);
		});
	});
});
