/**TODO: Drop it later - Should be using modals instead of alerts
 * Cross-platform alert utilities
 * Provides web-compatible alternatives to React Native Alert
 */

import { Platform, Alert } from 'react-native';

export const showAlert = (title: string, message?: string) => {
	if (Platform.OS === 'web') {
		window.alert(message ? `${title}\n\n${message}` : title);
	} else {
		// For native platforms, use React Native Alert
		Alert.alert(title, message);
	}
};

export const showConfirm = (
	title: string,
	message?: string,
	onConfirm?: () => void,
	onCancel?: () => void,
) => {
	if (Platform.OS === 'web') {
		const result = window.confirm(message ? `${title}\n\n${message}` : title);
		if (result && onConfirm) {
			onConfirm();
		} else if (!result && onCancel) {
			onCancel();
		}
	} else {
		// For native platforms, use React Native Alert with buttons
		Alert.alert(title, message, [
			{
				text: 'Cancel',
				style: 'cancel',
				onPress: onCancel,
			},
			{
				text: 'OK',
				style: 'destructive',
				onPress: onConfirm,
			},
		]);
	}
};

export const showAlertWithCallback = (title: string, message?: string, callback?: () => void) => {
	if (Platform.OS === 'web') {
		window.alert(message ? `${title}\n\n${message}` : title);
		if (callback) {
			callback();
		}
	} else {
		// For native platforms, use React Native Alert with callback
		Alert.alert(title, message, [
			{
				text: 'OK',
				onPress: callback,
			},
		]);
	}
};
