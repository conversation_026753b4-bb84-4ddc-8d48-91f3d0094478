/**
 * Validation Utilities
 *
 * Centralized validation functions and error types for consistent
 * validation across the application.
 */

import type { AuthData } from '@/repositories/secureStorageRepository';

/**
 * Custom validation error for domain-specific validation failures
 */
export class ValidationError extends Error {
	public readonly field: string;

	constructor(field: string, message: string) {
		super(`${field}: ${message}`);
		this.name = 'ValidationError';
		this.field = field;
	}
}

/**
 * Common validation utilities
 */
export const CommonValidators = {
	/**
	 * Validate that a value is a non-empty string
	 */
	requiredString: (value: any, fieldName: string): void => {
		if (!value || typeof value !== 'string') {
			throw new ValidationError(fieldName, `${fieldName} is required and must be a string`);
		}
	},

	/**
	 * Validate string with minimum length
	 */
	stringMinLength: (value: string, minLength: number, fieldName: string): void => {
		if (value.length < minLength) {
			throw new ValidationError(
				fieldName,
				`${fieldName} must be at least ${minLength} characters`,
			);
		}
	},

	/**
	 * Validate that a value is a positive number
	 */
	positiveNumber: (value: any, fieldName: string): void => {
		if (typeof value !== 'number' || value <= 0) {
			throw new ValidationError(fieldName, `${fieldName} must be a positive number`);
		}
	},

	/**
	 * Validate email format
	 */
	email: (value: string, fieldName: string): void => {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(value)) {
			throw new ValidationError(fieldName, `${fieldName} must be a valid email address`);
		}
	},

	/**
	 * Validate that a timestamp is in the future
	 */
	futureTimestamp: (timestamp: number, fieldName: string): void => {
		if (timestamp <= Date.now()) {
			throw new ValidationError(fieldName, `${fieldName} must be in the future`);
		}
	},
};

/**
 * Authentication data validation utilities
 */
export const AuthDataValidators = {
	/**
	 * Validate access token
	 */
	token: (token: string): void => {
		CommonValidators.requiredString(token, 'token');
		CommonValidators.stringMinLength(token, 10, 'token');
	},

	/**
	 * Validate refresh token
	 */
	refreshToken: (refreshToken: string): void => {
		CommonValidators.requiredString(refreshToken, 'refreshToken');
		CommonValidators.stringMinLength(refreshToken, 10, 'refreshToken');
	},

	/**
	 * Validate token expiration timestamp
	 */
	expiresAt: (expiresAt: number): void => {
		if (!expiresAt || typeof expiresAt !== 'number') {
			throw new ValidationError(
				'expiresAt',
				'Expiration time is required and must be a number',
			);
		}
		CommonValidators.futureTimestamp(expiresAt, 'expiresAt');
	},

	/**
	 * Validate user profile object
	 */
	userProfile: (userProfile: AuthData['userProfile']): void => {
		if (!userProfile) return;

		if (typeof userProfile !== 'object') {
			throw new ValidationError('userProfile', 'User profile must be an object');
		}

		// Validate required sub field
		CommonValidators.requiredString(userProfile.sub, 'userProfile.sub');

		// Validate optional fields if present
		if (userProfile.email) {
			CommonValidators.requiredString(userProfile.email, 'userProfile.email');
			CommonValidators.email(userProfile.email, 'userProfile.email');
		}

		if (userProfile.name) {
			CommonValidators.requiredString(userProfile.name, 'userProfile.name');
		}

		if (userProfile.preferred_username) {
			CommonValidators.requiredString(
				userProfile.preferred_username,
				'userProfile.preferred_username',
			);
		}

		if (userProfile.given_name) {
			CommonValidators.requiredString(userProfile.given_name, 'userProfile.given_name');
		}

		if (userProfile.family_name) {
			CommonValidators.requiredString(userProfile.family_name, 'userProfile.family_name');
		}
	},

	/**
	 * Validate complete AuthData object
	 */
	authData: (authData: AuthData): void => {
		AuthDataValidators.token(authData.token);
		AuthDataValidators.refreshToken(authData.refreshToken);
		AuthDataValidators.expiresAt(authData.expiresAt);

		// Validate optional fields if present
		if (authData.idToken) {
			CommonValidators.requiredString(authData.idToken, 'idToken');
		}

		if (authData.tokenType) {
			CommonValidators.requiredString(authData.tokenType, 'tokenType');
		}

		if (authData.scope) {
			CommonValidators.requiredString(authData.scope, 'scope');
		}

		AuthDataValidators.userProfile(authData.userProfile);
	},
};

/**
 * Storage key validation utilities
 */
export const StorageValidators = {
	/**
	 * Validate storage key
	 */
	key: (key: string): void => {
		CommonValidators.requiredString(key, 'key');
		if (key.trim().length === 0) {
			throw new ValidationError('key', 'Storage key cannot be empty or whitespace only');
		}
	},

	/**
	 * Validate storage data (cannot be null or undefined)
	 */
	data: <T>(data: T, fieldName: string = 'data'): void => {
		if (data === null || data === undefined) {
			throw new ValidationError(fieldName, 'Cannot store null or undefined data');
		}
	},
};

/**
 * Token update validation utilities
 */
export const TokenUpdateValidators = {
	/**
	 * Validate access token for updates
	 */
	accessToken: (accessToken: string): void => {
		CommonValidators.requiredString(accessToken, 'accessToken');
		CommonValidators.stringMinLength(accessToken, 10, 'accessToken');
	},

	/**
	 * Validate refresh token for updates (optional)
	 */
	refreshToken: (refreshToken?: string): void => {
		if (refreshToken) {
			CommonValidators.requiredString(refreshToken, 'refreshToken');
			CommonValidators.stringMinLength(refreshToken, 10, 'refreshToken');
		}
	},

	/**
	 * Validate expires in duration (optional)
	 */
	expiresIn: (expiresIn?: number): void => {
		if (expiresIn !== undefined) {
			CommonValidators.positiveNumber(expiresIn, 'expiresIn');
		}
	},
};
